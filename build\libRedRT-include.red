
red: context [
    #include %$ROOT-PATH$runtime/definitions.reds 
    #include %$ROOT-PATH$runtime/macros.reds 
    #include %$ROOT-PATH$runtime/structures.reds 
    cell!: alias struct! [
        header [integer!] 
        data1 [integer!] 
        data2 [integer!] 
        data3 [integer!]
    ] 
    series-buffer!: alias struct! [
        flags [integer!] 
        node [int-ptr!] 
        size [integer!] 
        offset [cell!] 
        tail [cell!]
    ] 
    root-base: as cell! 0 
    get-root: func [
        idx [integer!] 
        return: [red-block!]
    ] [
        as red-block! root-base + idx
    ] 
    get-root-node: func [
        idx [integer!] 
        return: [node!] 
        /local 
        obj [red-object!]
    ] [
        obj: as red-object! get-root idx 
        obj/ctx
    ] 
    #import [libRedRT-file stdcall [
            boot: "red/boot" [] 
            get-build-date: "red/get-build-date" [return: [c-string!]] 
            copy-cell: "red/copy-cell" [src [cell!] dst [cell!] return: [cell!]] 
            get-root-node2: "red/get-root-node2" [idx [integer!] return: [pointer! [integer!]]] 
            type-check-alt: "red/type-check-alt" [ref [cell!] expected [red-typeset!] index [integer!] arg [cell!] return: [cell!]] 
            type-check: "red/type-check" [expected [red-typeset!] index [integer!] arg [cell!] return: [cell!]] 
            eval-path*: "red/eval-path*" [[variadic] count [integer!] list [pointer! [integer!]]] 
            select-key*: "red/select-key*" [sub? [logic!] fetch? [logic!] return: [cell!]] 
            alloc-bytes: "red/alloc-bytes" [size [integer!] return: [pointer! [integer!]]] 
            alloc-cells: "red/alloc-cells" [size [integer!] return: [pointer! [integer!]]] 
            get-cmdline-args: "red/get-cmdline-args" [return: [cell!]] 
            set-opt-refinement*: "red/set-opt-refinement*" [value [cell!] idx [integer!] nb [integer!]] 
            call-with-array*: "red/call-with-array*" [[variadic] count [integer!] list [pointer! [integer!]] return: [integer!]] 
            get-libRedRT-bitarray: "red/get-libRedRT-bitarray" [return: [pointer! [integer!]]] 
            fire: "red/fire" [[variadic] count [integer!] list [pointer! [integer!]]] 
            set-type: "red/set-type" [cell [cell!] type [integer!]] 
            report: "red/report" [type [cell!] id [cell!] arg1 [cell!] arg2 [cell!] arg3 [cell!]] 
            f_routine: "f_routine" [] 
            f_also: "f_also" [] 
            f_attempt: "f_attempt" [] 
            f_comment: "f_comment" [] 
            f_quit: "f_quit" [] 
            f_empty?: "f_empty?" [] 
            f_??: "f_??" [] 
            f_probe: "f_probe" [] 
            f_quote: "f_quote" [] 
            f_first: "f_first" [] 
            f_second: "f_second" [] 
            f_third: "f_third" [] 
            f_fourth: "f_fourth" [] 
            f_fifth: "f_fifth" [] 
            f_last: "f_last" [] 
            f_spec-of: "f_spec-of" [] 
            f_body-of: "f_body-of" [] 
            f_words-of: "f_words-of" [] 
            f_class-of: "f_class-of" [] 
            f_values-of: "f_values-of" [] 
            f_bitset?: "f_bitset?" [] 
            f_binary?: "f_binary?" [] 
            f_block?: "f_block?" [] 
            f_char?: "f_char?" [] 
            f_email?: "f_email?" [] 
            f_file?: "f_file?" [] 
            f_float?: "f_float?" [] 
            f_get-path?: "f_get-path?" [] 
            f_get-word?: "f_get-word?" [] 
            f_hash?: "f_hash?" [] 
            f_integer?: "f_integer?" [] 
            f_issue?: "f_issue?" [] 
            f_lit-path?: "f_lit-path?" [] 
            f_lit-word?: "f_lit-word?" [] 
            f_logic?: "f_logic?" [] 
            f_map?: "f_map?" [] 
            f_none?: "f_none?" [] 
            f_pair?: "f_pair?" [] 
            f_paren?: "f_paren?" [] 
            f_path?: "f_path?" [] 
            f_percent?: "f_percent?" [] 
            f_refinement?: "f_refinement?" [] 
            f_set-path?: "f_set-path?" [] 
            f_set-word?: "f_set-word?" [] 
            f_string?: "f_string?" [] 
            f_tag?: "f_tag?" [] 
            f_time?: "f_time?" [] 
            f_typeset?: "f_typeset?" [] 
            f_tuple?: "f_tuple?" [] 
            f_unset?: "f_unset?" [] 
            f_url?: "f_url?" [] 
            f_word?: "f_word?" [] 
            f_image?: "f_image?" [] 
            f_date?: "f_date?" [] 
            f_money?: "f_money?" [] 
            f_ref?: "f_ref?" [] 
            f_point2D?: "f_point2D?" [] 
            f_point3D?: "f_point3D?" [] 
            f_handle?: "f_handle?" [] 
            f_error?: "f_error?" [] 
            f_action?: "f_action?" [] 
            f_native?: "f_native?" [] 
            f_datatype?: "f_datatype?" [] 
            f_function?: "f_function?" [] 
            f_object?: "f_object?" [] 
            f_op?: "f_op?" [] 
            f_routine?: "f_routine?" [] 
            f_vector?: "f_vector?" [] 
            f_any-list?: "f_any-list?" [] 
            f_any-block?: "f_any-block?" [] 
            f_any-function?: "f_any-function?" [] 
            f_any-object?: "f_any-object?" [] 
            f_any-path?: "f_any-path?" [] 
            f_any-string?: "f_any-string?" [] 
            f_any-word?: "f_any-word?" [] 
            f_series?: "f_series?" [] 
            f_number?: "f_number?" [] 
            f_immediate?: "f_immediate?" [] 
            f_scalar?: "f_scalar?" [] 
            f_all-word?: "f_all-word?" [] 
            f_any-point?: "f_any-point?" [] 
            f_to-bitset: "f_to-bitset" [] 
            f_to-binary: "f_to-binary" [] 
            f_to-block: "f_to-block" [] 
            f_to-char: "f_to-char" [] 
            f_to-email: "f_to-email" [] 
            f_to-file: "f_to-file" [] 
            f_to-float: "f_to-float" [] 
            f_to-get-path: "f_to-get-path" [] 
            f_to-get-word: "f_to-get-word" [] 
            f_to-hash: "f_to-hash" [] 
            f_to-integer: "f_to-integer" [] 
            f_to-issue: "f_to-issue" [] 
            f_to-lit-path: "f_to-lit-path" [] 
            f_to-lit-word: "f_to-lit-word" [] 
            f_to-logic: "f_to-logic" [] 
            f_to-map: "f_to-map" [] 
            f_to-none: "f_to-none" [] 
            f_to-pair: "f_to-pair" [] 
            f_to-paren: "f_to-paren" [] 
            f_to-path: "f_to-path" [] 
            f_to-percent: "f_to-percent" [] 
            f_to-refinement: "f_to-refinement" [] 
            f_to-set-path: "f_to-set-path" [] 
            f_to-set-word: "f_to-set-word" [] 
            f_to-string: "f_to-string" [] 
            f_to-tag: "f_to-tag" [] 
            f_to-time: "f_to-time" [] 
            f_to-typeset: "f_to-typeset" [] 
            f_to-tuple: "f_to-tuple" [] 
            f_to-unset: "f_to-unset" [] 
            f_to-url: "f_to-url" [] 
            f_to-word: "f_to-word" [] 
            f_to-image: "f_to-image" [] 
            f_to-date: "f_to-date" [] 
            f_to-money: "f_to-money" [] 
            f_to-ref: "f_to-ref" [] 
            f_to-point2D: "f_to-point2D" [] 
            f_to-point3D: "f_to-point3D" [] 
            f_context: "f_context" [] 
            f_alter: "f_alter" [] 
            f_offset?: "f_offset?" [] 
            f_repend: "f_repend" [] 
            f_replace: "f_replace" [] 
            f_math: "f_math" [] 
            f_charset: "f_charset" [] 
            f_ctx||182~on-parse-event: "f_ctx||182~on-parse-event" [octx [pointer! [integer!]]] 
            f_parse-trace: "f_parse-trace" [octx [pointer! [integer!]]] 
            f_suffix?: "f_suffix?" [] 
            f_scan: "f_scan" [] 
            f_load: "f_load" [] 
            f_save: "f_save" [] 
            f_cause-error: "f_cause-error" [] 
            f_pad: "f_pad" [] 
            f_mod: "f_mod" [] 
            f_modulo: "f_modulo" [] 
            f_eval-set-path: "f_eval-set-path" [] 
            f_to-red-file: "f_to-red-file" [] 
            f_dir?: "f_dir?" [] 
            f_normalize-dir: "f_normalize-dir" [] 
            f_what-dir: "f_what-dir" [] 
            f_change-dir: "f_change-dir" [] 
            f_make-dir: "f_make-dir" [] 
            f_extract: "f_extract" [] 
            f_extract-boot-args: "f_extract-boot-args" [] 
            f_collect: "f_collect" [] 
            f_flip-exe-flag: "f_flip-exe-flag" [] 
            f_split: "f_split" [] 
            f_dirize: "f_dirize" [] 
            f_clean-path: "f_clean-path" [] 
            f_split-path: "f_split-path" [] 
            f_do-file: "f_do-file" [] 
            f_path-thru: "f_path-thru" [] 
            f_exists-thru?: "f_exists-thru?" [] 
            f_read-thru: "f_read-thru" [] 
            f_load-thru: "f_load-thru" [] 
            f_do-thru: "f_do-thru" [] 
            f_cos: "f_cos" [] 
            f_sin: "f_sin" [] 
            f_tan: "f_tan" [] 
            f_acos: "f_acos" [] 
            f_asin: "f_asin" [] 
            f_atan: "f_atan" [] 
            f_atan2: "f_atan2" [] 
            f_sqrt: "f_sqrt" [] 
            f_to-UTC-date: "f_to-UTC-date" [] 
            f_to-local-date: "f_to-local-date" [] 
            f_show-memory-stats: "f_show-memory-stats" [] 
            f_transcode-trace: "f_transcode-trace" [] 
            f_rejoin: "f_rejoin" [] 
            f_sum: "f_sum" [] 
            f_average: "f_average" [] 
            f_last?: "f_last?" [] 
            f_dt: "f_dt" [] 
            f_clock: "f_clock" [] 
            f_ctx||265~interpreted?: "f_ctx||265~interpreted?" [octx [pointer! [integer!]]] 
            f_ctx||268~on-change*: "f_ctx||268~on-change*" [octx [pointer! [integer!]]] 
            f_ctx||275~on-change*: "f_ctx||275~on-change*" [octx [pointer! [integer!]]] 
            f_ctx||275~on-deep-change*: "f_ctx||275~on-deep-change*" [octx [pointer! [integer!]]] 
            f_ctx||281~on-change*: "f_ctx||281~on-change*" [octx [pointer! [integer!]]] 
            f_ctx||279~on-change*: "f_ctx||279~on-change*" [octx [pointer! [integer!]]] 
            f_ctx||279~on-deep-change*: "f_ctx||279~on-deep-change*" [octx [pointer! [integer!]]] 
            f_ctx||309~tracer: "f_ctx||309~tracer" [octx [pointer! [integer!]]] 
            f_ctx||327~on-change*: "f_ctx||327~on-change*" [octx [pointer! [integer!]]] 
            f_ctx||330~on-change*: "f_ctx||330~on-change*" [octx [pointer! [integer!]]] 
            f_ctx||330~on-deep-change*: "f_ctx||330~on-deep-change*" [octx [pointer! [integer!]]] 
            f_reactor: "f_reactor" [] 
            f_deep-reactor: "f_deep-reactor" [] 
            f_ctx||336~add-relation: "f_ctx||336~add-relation" [octx [pointer! [integer!]]] 
            f_ctx||336~identify-sources: "f_ctx||336~identify-sources" [octx [pointer! [integer!]]] 
            f_ctx||336~eval: "f_ctx||336~eval" [octx [pointer! [integer!]]] 
            f_ctx||336~eval-reaction: "f_ctx||336~eval-reaction" [octx [pointer! [integer!]]] 
            f_ctx||336~pending?: "f_ctx||336~pending?" [octx [pointer! [integer!]]] 
            f_ctx||336~check: "f_ctx||336~check" [octx [pointer! [integer!]]] 
            f_no-react: "f_no-react" [octx [pointer! [integer!]]] 
            f_stop-reactor: "f_stop-reactor" [octx [pointer! [integer!]]] 
            f_clear-reactions: "f_clear-reactions" [octx [pointer! [integer!]]] 
            f_dump-reactions: "f_dump-reactions" [octx [pointer! [integer!]]] 
            f_relate: "f_relate" [octx [pointer! [integer!]]] 
            f_is: "f_is" [octx [pointer! [integer!]]] 
            f_react?: "f_react?" [octx [pointer! [integer!]]] 
            f_react: "f_react" [octx [pointer! [integer!]]] 
            f_register-scheme: "f_register-scheme" [] 
            f_ctx||353~alpha-num+: "f_ctx||353~alpha-num+" [octx [pointer! [integer!]]] 
            f_ctx||353~parse-url: "f_ctx||353~parse-url" [octx [pointer! [integer!]]] 
            f_decode-url: "f_decode-url" [octx [pointer! [integer!]]] 
            f_encode-url: "f_encode-url" [octx [pointer! [integer!]]] 
            f_ctx||359~do-quit: "f_ctx||359~do-quit" [octx [pointer! [integer!]]] 
            f_ctx||359~throw-error: "f_ctx||359~throw-error" [octx [pointer! [integer!]]] 
            f_ctx||359~syntax-error: "f_ctx||359~syntax-error" [octx [pointer! [integer!]]] 
            f_ctx||359~do-safe: "f_ctx||359~do-safe" [octx [pointer! [integer!]]] 
            f_ctx||359~do-code: "f_ctx||359~do-code" [octx [pointer! [integer!]]] 
            f_ctx||359~rebind-all: "f_ctx||359~rebind-all" [octx [pointer! [integer!]]] 
            f_ctx||359~count-args: "f_ctx||359~count-args" [octx [pointer! [integer!]]] 
            f_ctx||359~arg-mode?: "f_ctx||359~arg-mode?" [octx [pointer! [integer!]]] 
            f_ctx||359~func-arity?: "f_ctx||359~func-arity?" [octx [pointer! [integer!]]] 
            f_ctx||359~value-path?: "f_ctx||359~value-path?" [octx [pointer! [integer!]]] 
            f_ctx||359~fetch-next: "f_ctx||359~fetch-next" [octx [pointer! [integer!]]] 
            f_ctx||359~eval: "f_ctx||359~eval" [octx [pointer! [integer!]]] 
            f_ctx||359~do-macro: "f_ctx||359~do-macro" [octx [pointer! [integer!]]] 
            f_ctx||359~register-macro: "f_ctx||359~register-macro" [octx [pointer! [integer!]]] 
            f_ctx||359~reset: "f_ctx||359~reset" [octx [pointer! [integer!]]] 
            f_ctx||359~expand: "f_ctx||359~expand" [octx [pointer! [integer!]]] 
            f_expand-directives: "f_expand-directives" [octx [pointer! [integer!]]] 
            f_ctx||378~calc-max: "f_ctx||378~calc-max" [octx [pointer! [integer!]]] 
            f_ctx||378~show-context: "f_ctx||378~show-context" [octx [pointer! [integer!]]] 
            f_ctx||378~show-parents: "f_ctx||378~show-parents" [octx [pointer! [integer!]]] 
            f_ctx||378~show-stack: "f_ctx||378~show-stack" [octx [pointer! [integer!]]] 
            f_ctx||378~show-watching: "f_ctx||378~show-watching" [octx [pointer! [integer!]]] 
            f_ctx||378~do-command: "f_ctx||378~do-command" [octx [pointer! [integer!]]] 
            f_ctx||378~debugger: "f_ctx||378~debugger" [octx [pointer! [integer!]]] 
            f_ctx||395~mold-part: "f_ctx||395~mold-part" [octx [pointer! [integer!]]] 
            f_ctx||395~dumper: "f_ctx||395~dumper" [octx [pointer! [integer!]]] 
            f_ctx||395~push: "f_ctx||395~push" [octx [pointer! [integer!]]] 
            f_ctx||395~drop: "f_ctx||395~drop" [octx [pointer! [integer!]]] 
            f_ctx||395~pop: "f_ctx||395~pop" [octx [pointer! [integer!]]] 
            f_ctx||395~top-of: "f_ctx||395~top-of" [octx [pointer! [integer!]]] 
            f_ctx||395~step: "f_ctx||395~step" [octx [pointer! [integer!]]] 
            f_ctx||404~put: "f_ctx||404~put" [octx [pointer! [integer!]]] 
            f_ctx||404~get: "f_ctx||404~get" [octx [pointer! [integer!]]] 
            f_ctx||408~save-level: "f_ctx||408~save-level" [octx [pointer! [integer!]]] 
            f_ctx||408~unroll-level: "f_ctx||408~unroll-level" [octx [pointer! [integer!]]] 
            f_ctx||408~reset: "f_ctx||408~reset" [octx [pointer! [integer!]]] 
            f_ctx||408~collector: "f_ctx||408~collector" [octx [pointer! [integer!]]] 
            f_ctx||395~guided-trace: "f_ctx||395~guided-trace" [octx [pointer! [integer!]]] 
            f_ctx||415~inspect: "f_ctx||415~inspect" [octx [pointer! [integer!]]] 
            f_ctx||378~profiler: "f_ctx||378~profiler" [octx [pointer! [integer!]]] 
            f_ctx||378~do-handler: "f_ctx||378~do-handler" [octx [pointer! [integer!]]] 
            f_profile: "f_profile" [octx [pointer! [integer!]]] 
            f_trace: "f_trace" [octx [pointer! [integer!]]] 
            f_debug: "f_debug" [octx [pointer! [integer!]]] 
            f_hex-to-rgb: "f_hex-to-rgb" [] 
            f_within?: "f_within?" [] 
            f_overlap?: "f_overlap?" [] 
            f_distance?: "f_distance?" [] 
            f_face?: "f_face?" [] 
            f_size-text: "f_size-text" [] 
            f_caret-to-offset: "f_caret-to-offset" [] 
            f_offset-to-caret: "f_offset-to-caret" [] 
            f_offset-to-char: "f_offset-to-char" [] 
            f_ctx||434~tail-idx?: "f_ctx||434~tail-idx?" [octx [pointer! [integer!]]] 
            f_ctx||434~push-color: "f_ctx||434~push-color" [octx [pointer! [integer!]]] 
            f_ctx||434~pop-color: "f_ctx||434~pop-color" [octx [pointer! [integer!]]] 
            f_ctx||434~close-colors: "f_ctx||434~close-colors" [octx [pointer! [integer!]]] 
            f_ctx||434~push: "f_ctx||434~push" [octx [pointer! [integer!]]] 
            f_ctx||434~pop: "f_ctx||434~pop" [octx [pointer! [integer!]]] 
            f_ctx||434~pop-all: "f_ctx||434~pop-all" [octx [pointer! [integer!]]] 
            f_ctx||434~optimize: "f_ctx||434~optimize" [octx [pointer! [integer!]]] 
            f_rtd-layout: "f_rtd-layout" [octx [pointer! [integer!]]] 
            f_ctx||432~line-height?: "f_ctx||432~line-height?" [octx [pointer! [integer!]]] 
            f_ctx||432~line-count?: "f_ctx||432~line-count?" [octx [pointer! [integer!]]] 
            f_metrics?: "f_metrics?" [] 
            f_set-flag: "f_set-flag" [] 
            f_debug-info?: "f_debug-info?" [] 
            f_on-face-deep-change*: "f_on-face-deep-change*" [] 
            f_link-tabs-to-parent: "f_link-tabs-to-parent" [] 
            f_link-sub-to-parent: "f_link-sub-to-parent" [] 
            f_update-font-faces: "f_update-font-faces" [] 
            f_ctx||454~on-change*: "f_ctx||454~on-change*" [octx [pointer! [integer!]]] 
            f_ctx||454~on-deep-change*: "f_ctx||454~on-deep-change*" [octx [pointer! [integer!]]] 
            f_ctx||458~on-change*: "f_ctx||458~on-change*" [octx [pointer! [integer!]]] 
            f_ctx||458~on-deep-change*: "f_ctx||458~on-deep-change*" [octx [pointer! [integer!]]] 
            f_ctx||462~on-change*: "f_ctx||462~on-change*" [octx [pointer! [integer!]]] 
            f_ctx||465~on-change*: "f_ctx||465~on-change*" [octx [pointer! [integer!]]] 
            f_ctx||468~capture-events: "f_ctx||468~capture-events" [octx [pointer! [integer!]]] 
            f_ctx||468~awake: "f_ctx||468~awake" [octx [pointer! [integer!]]] 
            f_ctx||476~init: "f_ctx||476~init" [octx [pointer! [integer!]]] 
            f_draw: "f_draw" [] 
            f_ctx||484~color-backgrounds: "f_ctx||484~color-backgrounds" [octx [pointer! [integer!]]] 
            f_ctx||484~color-tabpanel-children: "f_ctx||484~color-tabpanel-children" [octx [pointer! [integer!]]] 
            f_ctx||484~OK-Cancel: "f_ctx||484~OK-Cancel" [octx [pointer! [integer!]]] 
            f_ctx||482~process: "f_ctx||482~process" [octx [pointer! [integer!]]] 
            f_ctx||480~throw-error: "f_ctx||480~throw-error" [octx [pointer! [integer!]]] 
            f_ctx||480~process-reactors: "f_ctx||480~process-reactors" [octx [pointer! [integer!]]] 
            f_ctx||480~opt-as-integer: "f_ctx||480~opt-as-integer" [octx [pointer! [integer!]]] 
            f_ctx||480~calc-size: "f_ctx||480~calc-size" [octx [pointer! [integer!]]] 
            f_ctx||480~align-faces: "f_ctx||480~align-faces" [octx [pointer! [integer!]]] 
            f_ctx||480~resize-child-panels: "f_ctx||480~resize-child-panels" [octx [pointer! [integer!]]] 
            f_ctx||480~clean-style: "f_ctx||480~clean-style" [octx [pointer! [integer!]]] 
            f_ctx||480~process-draw: "f_ctx||480~process-draw" [octx [pointer! [integer!]]] 
            f_ctx||480~pre-load: "f_ctx||480~pre-load" [octx [pointer! [integer!]]] 
            f_ctx||480~preset-focus: "f_ctx||480~preset-focus" [octx [pointer! [integer!]]] 
            f_ctx||480~add-option: "f_ctx||480~add-option" [octx [pointer! [integer!]]] 
            f_ctx||480~add-flag: "f_ctx||480~add-flag" [octx [pointer! [integer!]]] 
            f_ctx||480~add-bounds: "f_ctx||480~add-bounds" [octx [pointer! [integer!]]] 
            f_ctx||480~fetch-value: "f_ctx||480~fetch-value" [octx [pointer! [integer!]]] 
            f_ctx||480~fetch-argument: "f_ctx||480~fetch-argument" [octx [pointer! [integer!]]] 
            f_ctx||480~fetch-expr: "f_ctx||480~fetch-expr" [octx [pointer! [integer!]]] 
            f_ctx||480~fetch-options: "f_ctx||480~fetch-options" [octx [pointer! [integer!]]] 
            f_ctx||480~make-actor: "f_ctx||480~make-actor" [octx [pointer! [integer!]]] 
            f_layout: "f_layout" [octx [pointer! [integer!]]] 
            f_do-events: "f_do-events" [] 
            f_stop-events: "f_stop-events" [] 
            f_do-safe: "f_do-safe" [] 
            f_do-actor: "f_do-actor" [] 
            f_show: "f_show" [] 
            f_unview: "f_unview" [] 
            f_view: "f_view" [] 
            f_center-face: "f_center-face" [] 
            f_make-face: "f_make-face" [] 
            f_dump-face: "f_dump-face" [] 
            f_do-no-sync: "f_do-no-sync" [] 
            f_get-scroller: "f_get-scroller" [] 
            f_get-face-pane: "f_get-face-pane" [] 
            f_get-focusable: "f_get-focusable" [] 
            f_insert-event-func: "f_insert-event-func" [] 
            f_remove-event-func: "f_remove-event-func" [] 
            f_request-font: "f_request-font" [] 
            f_request-file: "f_request-file" [] 
            f_request-dir: "f_request-dir" [] 
            f_set-focus: "f_set-focus" [] 
            f_foreach-face: "f_foreach-face" [] 
            f_alert: "f_alert" [] 
            f_~anon533~: "f_~anon533~" [] 
            f_~anon535~: "f_~anon535~" [] 
            f_ctx||537~encode: "f_ctx||537~encode" [octx [pointer! [integer!]]] 
            f_ctx||537~decode: "f_ctx||537~decode" [octx [pointer! [integer!]]] 
            f_ctx||544~to-csv-line: "f_ctx||544~to-csv-line" [octx [pointer! [integer!]]] 
            f_ctx||544~escape-value: "f_ctx||544~escape-value" [octx [pointer! [integer!]]] 
            f_ctx||544~next-column-name: "f_ctx||544~next-column-name" [octx [pointer! [integer!]]] 
            f_ctx||544~make-header: "f_ctx||544~make-header" [octx [pointer! [integer!]]] 
            f_ctx||544~get-columns: "f_ctx||544~get-columns" [octx [pointer! [integer!]]] 
            f_ctx||544~encode-map: "f_ctx||544~encode-map" [octx [pointer! [integer!]]] 
            f_ctx||544~encode-maps: "f_ctx||544~encode-maps" [octx [pointer! [integer!]]] 
            f_ctx||544~encode-flat: "f_ctx||544~encode-flat" [octx [pointer! [integer!]]] 
            f_ctx||544~encode-blocks: "f_ctx||544~encode-blocks" [octx [pointer! [integer!]]] 
            f_load-csv: "f_load-csv" [octx [pointer! [integer!]]] 
            f_to-csv: "f_to-csv" [octx [pointer! [integer!]]] 
            f_ctx||557~decode-unicode-char: "f_ctx||557~decode-unicode-char" [octx [pointer! [integer!]]] 
            f_ctx||557~push: "f_ctx||557~push" [octx [pointer! [integer!]]] 
            f_ctx||557~pop: "f_ctx||557~pop" [octx [pointer! [integer!]]] 
            f_ctx||557~emit: "f_ctx||557~emit" [octx [pointer! [integer!]]] 
            f_load-json: "f_load-json" [octx [pointer! [integer!]]] 
            f_ctx||564~init-state: "f_ctx||564~init-state" [octx [pointer! [integer!]]] 
            f_ctx||564~emit-indent: "f_ctx||564~emit-indent" [octx [pointer! [integer!]]] 
            f_ctx||564~emit-key-value: "f_ctx||564~emit-key-value" [octx [pointer! [integer!]]] 
            f_ctx||564~red-to-json-value: "f_ctx||564~red-to-json-value" [octx [pointer! [integer!]]] 
            f_to-json: "f_to-json" [octx [pointer! [integer!]]] 
            f_ctx||571~encode: "f_ctx||571~encode" [octx [pointer! [integer!]]] 
            f_ctx||571~decode: "f_ctx||571~decode" [octx [pointer! [integer!]]] 
            f_keep: "f_keep" [] 
            quit-return: "quit-return" [status [integer!]] 
            set-quiet: "set-quiet" [word [cell!] value [cell!] return: [cell!]] 
            set-slot-quiet: "set-slot-quiet" [series [cell!] value [cell!]] 
            shift-right: "shift-right" [data [integer!] bits [integer!]] 
            shift-left: "shift-left" [data [integer!] bits [integer!]] 
            shift-logical: "shift-logical" [data [integer!] bits [integer!]] 
            last-lf?: "last-lf?" [] 
            get-current-dir: "get-current-dir" [] 
            set-current-dir: "set-current-dir" [path [red-file!]] 
            create-dir: "create-dir" [path [red-file!]] 
            exists?: "exists?" [path [red-file!] return: [logic!]] 
            os-info: "os-info" [] 
            as-color: "as-color" [r [integer!] g [integer!] b [integer!]] 
            as-ipv4: "as-ipv4" [a [integer!] b [integer!] c [integer!] d [integer!]] 
            as-rgba: "as-rgba" [r [integer!] g [integer!] b [integer!] a [integer!]] 
            count-chars: "count-chars" [start [red-binary!] pos [red-binary!] return: [integer!]] 
            stack-size?: "stack-size?" [return: [integer!]] 
            pick-stack: "pick-stack" [idx [integer!]] 
            frame-index?: "frame-index?" [return: [integer!]] 
            collect-calls: "collect-calls" [blk [red-block!]] 
            tracing?: "tracing?" [] 
            read-clipboard: "read-clipboard" [return: [cell!]] 
            write-clipboard: "write-clipboard" [data [cell!] return: [logic!]] 
            write-stdout: "write-stdout" [data [cell!]] 
            __make-sys-object: "__make-sys-object" [] 
            ctx||312~encode: "ctx||312~encode" [img [red-image!] where [cell!]] 
            ctx||312~decode: "ctx||312~decode" [data [cell!]] 
            ctx||315~encode: "ctx||315~encode" [img [red-image!] where [cell!]] 
            ctx||315~decode: "ctx||315~decode" [data [cell!]] 
            ctx||318~encode: "ctx||318~encode" [img [red-image!] where [cell!]] 
            ctx||318~decode: "ctx||318~decode" [data [cell!]] 
            ctx||321~encode: "ctx||321~encode" [img [red-image!] where [cell!]] 
            ctx||321~decode: "ctx||321~decode" [data [cell!]] 
            ctx||324~encode: "ctx||324~encode" [data [cell!] where [cell!]] 
            ctx||324~decode: "ctx||324~decode" [payload [cell!]] 
            event?: "event?" [value [cell!] return: [logic!]] 
            find-flag?: "find-flag?" [facet [cell!] flag [red-word!]] 
            ctx||476~make-null-handle: "ctx||476~make-null-handle" [] 
            ctx||476~get-screen-size: "ctx||476~get-screen-size" [id [integer!]] 
            ctx||476~size-text: "ctx||476~size-text" [face [red-object!] value [cell!]] 
            ctx||476~on-change-facet: "ctx||476~on-change-facet" [owner [red-object!] word [red-word!] value [cell!] action [red-word!] new [cell!] index [integer!] part [integer!]] 
            ctx||476~update-text: "ctx||476~update-text" [face [red-object!]] 
            ctx||476~update-font: "ctx||476~update-font" [font [red-object!] flags [integer!]] 
            ctx||476~update-para: "ctx||476~update-para" [face [red-object!] flags [integer!]] 
            ctx||476~destroy-view: "ctx||476~destroy-view" [face [red-object!] empty? [logic!]] 
            ctx||476~detach-image: "ctx||476~detach-image" [img [red-image!]] 
            ctx||476~update-view: "ctx||476~update-view" [face [red-object!]] 
            ctx||476~refresh-window: "ctx||476~refresh-window" [h [red-handle!]] 
            ctx||476~redraw: "ctx||476~redraw" [face [red-object!]] 
            ctx||476~show-window: "ctx||476~show-window" [id [red-handle!]] 
            ctx||476~make-view: "ctx||476~make-view" [face [red-object!] parent [red-handle!]] 
            ctx||476~draw-image: "ctx||476~draw-image" [image [red-image!] cmds [red-block!]] 
            ctx||476~draw-face: "ctx||476~draw-face" [face [red-object!] cmds [red-block!]] 
            ctx||476~do-event-loop: "ctx||476~do-event-loop" [no-wait? [logic!]] 
            ctx||476~exit-event-loop: "ctx||476~exit-event-loop" [] 
            ctx||476~request-font: "ctx||476~request-font" [font [red-object!] selected [cell!] mono? [logic!]] 
            ctx||476~request-file: "ctx||476~request-file" [title [cell!] name [cell!] filter [cell!] save? [logic!] multi? [logic!]] 
            ctx||476~request-dir: "ctx||476~request-dir" [title [cell!] dir [cell!] filter [cell!] keep? [logic!] multi? [logic!]] 
            ctx||476~text-box-metrics: "ctx||476~text-box-metrics" [box [red-object!] arg0 [cell!] type [integer!]] 
            ctx||476~update-scroller: "ctx||476~update-scroller" [scroller [red-object!] flags [integer!]] 
            ctx||476~set-dark-mode: "ctx||476~set-dark-mode" [face [red-object!] dark? [logic!]] 
            ctx||476~support-dark-mode?: "ctx||476~support-dark-mode?" [return: [logic!]] 
            throw-draw-error: "throw-draw-error" [cmds [red-block!] cmd [cell!] catch? [logic!]] 
            _throw-draw-error: "_throw-draw-error" [cmds [red-block!] cmd [cell!] cat [cell!] id [cell!] catch? [logic!]] 
            transparent-color?: "transparent-color?" [color [red-tuple!] return: [logic!]] 
            get-color-int: "get-color-int" [tp [red-tuple!] alpha? [pointer! [integer!]] return: [integer!]] 
            get-float: "get-float" [int [red-integer!] return: [float!]] 
            get-float32: "get-float32" [int [red-integer!] return: [float32!]] 
            reverse-int-array: "reverse-int-array" [array [pointer! [integer!]] count [integer!]] 
            reverse-float32-array: "reverse-float32-array" [array [pointer! [float32!]] count [integer!]] 
            old-gradient-pen: "old-gradient-pen" [DC [draw-ctx!] cmds [red-block!] start [cell!] tail [cell!] cmd [cell!] sym [integer!] catch? [logic!] return: [cell!]] 
            check-pen: "check-pen" [DC [draw-ctx!] cmds [red-block!] start [cell!] tail [cell!] cmd [cell!] sym [integer!] catch? [logic!] return: [cell!]] 
            check-line: "check-line" [DC [draw-ctx!] cmds [red-block!] start [cell!] tail [cell!] cmd [cell!] sym [integer!] catch? [logic!] return: [cell!]] 
            parse-shape: "parse-shape" [DC [draw-ctx!] cmds [red-block!] draw? [logic!] catch? [logic!] sub? [logic!]] 
            parse-draw: "parse-draw" [DC [draw-ctx!] cmds [red-block!] catch? [logic!]] 
            do-draw: "do-draw" [handle [pointer! [integer!]] img [red-image!] cmds [red-block!] on-graphic? [logic!] cache? [logic!] paint? [logic!] catch? [logic!]] 
            parse-text-styles: "parse-text-styles" [dc [pointer! [integer!]] layout [pointer! [integer!]] cmds [red-block!] text [red-string!] catch? [logic!]] 
            ctx||557~unescape: "ctx||557~unescape" [str [red-string!] return: [red-string!]] 
            f_time-it: "f_dt" [] 
            f_single?: "f_last?" [] 
            f_keys-of: "f_words-of" [] 
            f_object: "f_context" [] 
            f_halt: "f_quit" [] 
            f_ctx||395~emit: "red/natives/print*" [check? [logic!]] 
            root: "red/root" [red-block!] 
            stk-bottom: "red/stk-bottom" [int-ptr!] 
            unset-value: "red/unset-value" [cell!] 
            none-value: "red/none-value" [cell!] 
            true-value: "red/true-value" [cell!] 
            false-value: "red/false-value" [cell!] 
            boot?: "red/boot?" [logic!]
        ]] 
    redbin: context [
        #import [libRedRT-file stdcall [
                boot-load: "red/redbin/boot-load" [payload [pointer! [byte!]] keep? [logic!] return: [cell!]]
            ]]
    ] 
    platform: context [
        #import [libRedRT-file stdcall [
                prin*: "red/platform/prin*" [s [c-string!] return: [c-string!]] 
                prin-int*: "red/platform/prin-int*" [i [integer!] return: [integer!]] 
                prin-hex*: "red/platform/prin-hex*" [i [integer!] return: [integer!]] 
                prin-2hex*: "red/platform/prin-2hex*" [i [integer!] return: [integer!]] 
                prin-float*: "red/platform/prin-float*" [f [float!] return: [float!]] 
                prin-float32*: "red/platform/prin-float32*" [f [float32!] return: [float32!]]
            ]]
    ] 
    stack: context [
        #import [libRedRT-file stdcall [
                mark: "red/stack/mark" [fun [red-word!] type [integer!]] 
                mark-native: "red/stack/mark-native" [fun [red-word!]] 
                mark-func: "red/stack/mark-func" [fun [red-word!] ctx-name [pointer! [integer!]]] 
                mark-loop: "red/stack/mark-loop" [fun [red-word!]] 
                mark-try: "red/stack/mark-try" [fun [red-word!]] 
                mark-try-all: "red/stack/mark-try-all" [fun [red-word!]] 
                mark-catch: "red/stack/mark-catch" [fun [red-word!]] 
                mark-func-body: "red/stack/mark-func-body" [fun [red-word!]] 
                unwind: "red/stack/unwind" [] 
                unwind-last: "red/stack/unwind-last" [return: [cell!]] 
                reset: "red/stack/reset" [return: [cell!]] 
                keep: "red/stack/keep" [return: [cell!]] 
                push: "red/stack/push" [value [cell!] return: [cell!]] 
                unroll: "red/stack/unroll" [flags [integer!]] 
                unroll-loop: "red/stack/unroll-loop" [inner? [logic!]] 
                revert: "red/stack/revert" [] 
                adjust-post-try: "red/stack/adjust-post-try" [] 
                pop: "red/stack/pop" [positions [integer!]] 
                set-last: "red/stack/set-last" [last [cell!] return: [cell!]] 
                push*: "red/stack/push*" [return: [cell!]] 
                arguments: "red/stack/arguments" [cell!] 
                top: "red/stack/top" [cell!] 
                bottom: "red/stack/bottom" [cell!]
            ]] 
        #enum flags! [FRAME_FUNCTION: 16777216]
    ] 
    interpreter: context [
        #import [libRedRT-file stdcall [
                eval-path: "red/interpreter/eval-path" [value [cell!] pc [cell!] end [cell!] code [red-block!] set? [logic!] get? [logic!] sub? [logic!] case? [logic!] return: [cell!]]
            ]]
    ] 
    lexer: context [
        #import [libRedRT-file stdcall [
                scan: "red/lexer/scan" [dst [cell!] src [pointer! [byte!]] size [integer!] one? [logic!] scan? [logic!] load? [logic!] wrap? [logic!] len [pointer! [integer!]] fun [red-function!] ser [red-series!] out [red-block!] return: [integer!]] 
                scan-alt: "red/lexer/scan-alt" [dst [cell!] str [red-string!] size [integer!] one? [logic!] scan? [logic!] load? [logic!] wrap? [logic!] len [pointer! [integer!]] fun [red-function!] out [red-block!] return: [integer!]]
            ]]
    ] 
    none: context [
        #import [libRedRT-file stdcall [
                push-last: "red/none/push-last" [return: [cell!]] 
                push: "red/none/push" [return: [cell!]]
            ]]
    ] 
    logic: context [
        #import [libRedRT-file stdcall [
                false?: "red/logic/false?" [return: [logic!]] 
                true?: "red/logic/true?" [return: [logic!]] 
                push: "red/logic/push" [value [logic!] return: [red-logic!]] 
                get: "red/logic/get" [value [cell!] return: [logic!]] 
                box: "red/logic/box" [value [logic!] return: [red-logic!]] 
                make-in: "red/logic/make-in" [parent [red-block!] value [logic!] return: [red-logic!]]
            ]]
    ] 
    refinement: context [
        #import [libRedRT-file stdcall [
                push-local: "red/refinement/push-local" [node [pointer! [integer!]] index [integer!] return: [red-refinement!]] 
                push: "red/refinement/push" [w [red-word!]]
            ]]
    ] 
    lit-word: context [
        #import [libRedRT-file stdcall [
                push-local: "red/lit-word/push-local" [node [pointer! [integer!]] index [integer!] return: [red-word!]] 
                push: "red/lit-word/push" [w [red-word!] return: [red-word!]]
            ]]
    ] 
    binary: context [
        #import [libRedRT-file stdcall [
                push: "red/binary/push" [bin [red-binary!]] 
                rs-head: "red/binary/rs-head" [bin [red-binary!] return: [pointer! [byte!]]] 
                rs-tail: "red/binary/rs-tail" [bin [red-binary!] return: [pointer! [byte!]]] 
                rs-length?: "red/binary/rs-length?" [bin [red-binary!] return: [integer!]]
            ]]
    ] 
    block: context [
        #import [libRedRT-file stdcall [
                push: "red/block/push" [blk [red-block!]] 
                push-only*: "red/block/push-only*" [size [integer!] return: [red-block!]] 
                insert-thru: "red/block/insert-thru" [] 
                append-thru: "red/block/append-thru" [] 
                rs-head: "red/block/rs-head" [blk [red-block!] return: [cell!]] 
                rs-next: "red/block/rs-next" [blk [red-block!] return: [logic!]] 
                rs-tail?: "red/block/rs-tail?" [blk [red-block!] return: [logic!]] 
                rs-length?: "red/block/rs-length?" [blk [red-block!] return: [integer!]] 
                rs-abs-at: "red/block/rs-abs-at" [blk [red-block!] pos [integer!] return: [cell!]] 
                rs-append: "red/block/rs-append" [blk [red-block!] value [cell!] return: [cell!]] 
                rs-tail: "red/block/rs-tail" [blk [red-block!] return: [cell!]] 
                rs-clear: "red/block/rs-clear" [blk [red-block!]] 
                make-at: "red/block/make-at" [blk [red-block!] size [integer!] return: [red-block!]] 
                select-word: "red/block/select-word" [blk [red-block!] word [red-word!] case? [logic!] return: [cell!]] 
                find: "red/block/find" [blk [red-block!] value [cell!] part [cell!] only? [logic!] case? [logic!] same? [logic!] any? [logic!] with-arg [red-string!] skip [red-integer!] last? [logic!] reverse? [logic!] tail? [logic!] match? [logic!] return: [cell!]]
            ]]
    ] 
    char: context [
        #import [libRedRT-file stdcall [
                push: "red/char/push" [value [integer!] return: [red-char!]]
            ]]
    ] 
    datatype: context [
        #import [libRedRT-file stdcall [
                push: "red/datatype/push" [type [integer!] return: [red-datatype!]] 
                register: "red/datatype/register" [[variadic] count [integer!] list [pointer! [integer!]]]
            ]]
    ] 
    date: context [
        #import [libRedRT-file stdcall [
                push: "red/date/push" [date [integer!] time [float!] return: [red-date!]]
            ]]
    ] 
    email: context [
        #import [libRedRT-file stdcall [
                push: "red/email/push" [email [red-email!]]
            ]]
    ] 
    file: context [
        #import [libRedRT-file stdcall [
                push: "red/file/push" [file [red-file!]]
            ]]
    ] 
    float: context [
        #import [libRedRT-file stdcall [
                push: "red/float/push" [value [float!] return: [red-float!]] 
                push64: "red/float/push64" [high [integer!] low [integer!] return: [red-float!]] 
                get: "red/float/get" [value [cell!] return: [float!]] 
                box: "red/float/box" [value [float!] return: [red-float!]]
            ]]
    ] 
    _function: context [
        #import [libRedRT-file stdcall [
                push: "red/_function/push" [spec [red-block!] body [red-block!] ctx [pointer! [integer!]] code [integer!] obj-ctx [pointer! [integer!]] flags [integer!] return: [pointer! [integer!]]] 
                init-locals: "red/_function/init-locals" [nb [integer!]]
            ]]
    ] 
    get-path: context [
        #import [libRedRT-file stdcall [
                push: "red/get-path/push" [p [red-block!]]
            ]]
    ] 
    get-word: context [
        #import [libRedRT-file stdcall [
                push: "red/get-word/push" [w [red-word!] return: [red-word!]] 
                get: "red/get-word/get" [word [red-word!] return: [cell!]] 
                push-local: "red/get-word/push-local" [node [pointer! [integer!]] index [integer!] return: [red-word!]]
            ]]
    ] 
    integer: context [
        #import [libRedRT-file stdcall [
                push: "red/integer/push" [value [integer!] return: [red-integer!]] 
                get-any*: "red/integer/get-any*" [return: [integer!]] 
                get*: "red/integer/get*" [return: [integer!]] 
                get: "red/integer/get" [value [cell!] return: [integer!]] 
                make-at: "red/integer/make-at" [slot [cell!] value [integer!] return: [red-integer!]] 
                form-signed: "red/integer/form-signed" [i [integer!] return: [c-string!]] 
                box: "red/integer/box" [value [integer!] return: [red-integer!]] 
                make-in: "red/integer/make-in" [parent [red-block!] value [integer!] return: [red-integer!]]
            ]]
    ] 
    issue: context [
        #import [libRedRT-file stdcall [
                push: "red/issue/push" [w [red-word!]]
            ]]
    ] 
    lit-path: context [
        #import [libRedRT-file stdcall [
                push: "red/lit-path/push" [p [red-block!]]
            ]]
    ] 
    map: context [
        #import [libRedRT-file stdcall [
                push: "red/map/push" [map [red-hash!]]
            ]]
    ] 
    money: context [
        #import [libRedRT-file stdcall [
                push: "red/money/push" [sign [logic!] currency [integer!] amount [c-string!] return: [red-money!]]
            ]]
    ] 
    object: context [
        #import [libRedRT-file stdcall [
                push: "red/object/push" [ctx [pointer! [integer!]] evt [pointer! [integer!]] class [integer!] idx-s [integer!] loc-s [integer!] idx-d [integer!] loc-d [integer!] return: [red-object!]] 
                clone-series: "red/object/clone-series" [src [pointer! [integer!]] dst [pointer! [integer!]] copy? [logic!]] 
                transfer: "red/object/transfer" [src [pointer! [integer!]] dst [pointer! [integer!]]] 
                init-push: "red/object/init-push" [node [pointer! [integer!]] class [integer!] return: [red-object!]] 
                init-events: "red/object/init-events" [ctx [pointer! [integer!]] idx-s [integer!] loc-s [integer!] idx-d [integer!] loc-d [integer!] return: [pointer! [integer!]]] 
                loc-fire-on-set*: "red/object/loc-fire-on-set*" [parent [cell!] field [red-word!]] 
                loc-ctx-fire-on-set*: "red/object/loc-ctx-fire-on-set*" [parent-ctx [pointer! [integer!]] field [red-word!]] 
                fire-on-set*: "red/object/fire-on-set*" [parent [red-word!] field [red-word!]] 
                get-values: "red/object/get-values" [obj [red-object!] return: [cell!]] 
                unchanged?: "red/object/unchanged?" [word [red-word!] id [integer!] return: [logic!]] 
                unchanged2?: "red/object/unchanged2?" [node [pointer! [integer!]] index [integer!] id [integer!] return: [logic!]] 
                get-word: "red/object/get-word" [obj [pointer! [integer!]] index [integer!] return: [cell!]] 
                rs-find: "red/object/rs-find" [obj [red-object!] value [cell!] return: [integer!]]
            ]]
    ] 
    pair: context [
        #import [libRedRT-file stdcall [
                push: "red/pair/push" [x [integer!] y [integer!] return: [red-pair!]]
            ]]
    ] 
    paren: context [
        #import [libRedRT-file stdcall [
                push: "red/paren/push" [paren [red-paren!]]
            ]]
    ] 
    path: context [
        #import [libRedRT-file stdcall [
                push: "red/path/push" [p [red-path!]]
            ]]
    ] 
    percent: context [
        #import [libRedRT-file stdcall [
                push: "red/percent/push" [value [float!] return: [red-float!]] 
                push64: "red/percent/push64" [high [integer!] low [integer!] return: [red-float!]]
            ]]
    ] 
    point2D: context [
        #import [libRedRT-file stdcall [
                push: "red/point2D/push" [x [float32!] y [float32!] return: [red-point2D!]]
            ]]
    ] 
    point3D: context [
        #import [libRedRT-file stdcall [
                push: "red/point3D/push" [x [float32!] y [float32!] z [float32!] return: [red-point3D!]]
            ]]
    ] 
    ref: context [
        #import [libRedRT-file stdcall [
                push: "red/ref/push" [ref [red-ref!]]
            ]]
    ] 
    routine: context [
        #import [libRedRT-file stdcall [
                push: "red/routine/push" [spec [red-block!] body [red-block!] code [integer!] ret-type [integer!] extern? [logic!] return: [red-routine!]]
            ]]
    ] 
    set-path: context [
        #import [libRedRT-file stdcall [
                push: "red/set-path/push" [p [red-block!]]
            ]]
    ] 
    set-word: context [
        #import [libRedRT-file stdcall [
                push: "red/set-word/push" [w [red-word!] return: [red-word!]] 
                push-local: "red/set-word/push-local" [node [pointer! [integer!]] index [integer!] return: [red-word!]]
            ]]
    ] 
    string: context [
        #import [libRedRT-file stdcall [
                push: "red/string/push" [str [red-string!] return: [red-string!]] 
                rs-head: "red/string/rs-head" [str [red-string!] return: [pointer! [byte!]]] 
                rs-tail?: "red/string/rs-tail?" [str [red-string!] return: [logic!]] 
                equal?: "red/string/equal?" [str1 [red-string!] str2 [red-string!] op [integer!] match? [logic!] return: [integer!]] 
                rs-make-at: "red/string/rs-make-at" [slot [cell!] size [integer!] return: [red-string!]] 
                get-char: "red/string/get-char" [p [pointer! [byte!]] unit [integer!] return: [integer!]] 
                rs-reset: "red/string/rs-reset" [str [red-string!]] 
                concatenate: "red/string/concatenate" [str1 [red-string!] str2 [red-string!] part [integer!] offset [integer!] keep? [logic!] insert? [logic!]] 
                rs-length?: "red/string/rs-length?" [str [red-string!] return: [integer!]] 
                concatenate-literal: "red/string/concatenate-literal" [str [red-string!] p [c-string!] return: [series-buffer!]] 
                append-char: "red/string/append-char" [s [series-buffer!] cp [integer!] return: [series-buffer!]] 
                insert-char: "red/string/insert-char" [s [series-buffer!] offset [integer!] cp [integer!] return: [series-buffer!]] 
                rs-abs-length?: "red/string/rs-abs-length?" [str [red-string!] return: [integer!]] 
                remove-char: "red/string/remove-char" [str [red-string!] offset [integer!] return: [red-string!]] 
                poke-char: "red/string/poke-char" [s [series-buffer!] p [pointer! [byte!]] cp [integer!] return: [series-buffer!]] 
                remove-part: "red/string/remove-part" [str [red-string!] offset [integer!] part [integer!] return: [red-string!]] 
                to-hex: "red/string/to-hex" [value [integer!] char? [logic!] return: [c-string!]] 
                make-at: "red/string/make-at" [slot [cell!] size [integer!] unit [integer!] return: [red-string!]] 
                load: "red/string/load" [src [c-string!] size [integer!] encoding [integer!] return: [red-string!]] 
                load-at: "red/string/load-at" [src [c-string!] size [integer!] slot [cell!] encoding [integer!] return: [red-string!]]
            ]]
    ] 
    tag: context [
        #import [libRedRT-file stdcall [
                push: "red/tag/push" [tag [red-tag!]]
            ]]
    ] 
    time: context [
        #import [libRedRT-file stdcall [
                push: "red/time/push" [time [float!] return: [red-time!]]
            ]]
    ] 
    tuple: context [
        #import [libRedRT-file stdcall [
                push: "red/tuple/push" [size [integer!] arr1 [integer!] arr2 [integer!] arr3 [integer!] return: [red-tuple!]]
            ]]
    ] 
    typeset: context [
        #import [libRedRT-file stdcall [
                push: "red/typeset/push" [sets [red-typeset!]]
            ]]
    ] 
    unset: context [
        #import [libRedRT-file stdcall [
                push: "red/unset/push" [return: [red-unset!]]
            ]]
    ] 
    url: context [
        #import [libRedRT-file stdcall [
                push: "red/url/push" [url [red-url!]]
            ]]
    ] 
    vector: context [
        #import [libRedRT-file stdcall [
                push: "red/vector/push" [vec [red-vector!]] 
                rs-head: "red/vector/rs-head" [vec [red-vector!] return: [pointer! [byte!]]] 
                rs-tail: "red/vector/rs-tail" [vec [red-vector!] return: [pointer! [byte!]]] 
                rs-tail?: "red/vector/rs-tail?" [vec [red-vector!] return: [logic!]] 
                rs-length?: "red/vector/rs-length?" [vec [red-vector!] return: [integer!]] 
                rs-skip: "red/vector/rs-skip" [vec [red-vector!] len [integer!] return: [logic!]] 
                rs-next: "red/vector/rs-next" [vec [red-vector!] return: [logic!]] 
                rs-clear: "red/vector/rs-clear" [vec [red-vector!]] 
                rs-append: "red/vector/rs-append" [vec [red-vector!] value [cell!] return: [cell!]] 
                rs-append-int: "red/vector/rs-append-int" [vec [red-vector!] n [integer!]] 
                rs-overwrite: "red/vector/rs-overwrite" [vec [red-vector!] offset [integer!] value [cell!] return: [series-buffer!]] 
                rs-insert: "red/vector/rs-insert" [vec [red-vector!] offset [integer!] value [cell!] return: [series-buffer!]] 
                get-value: "red/vector/get-value" [p [pointer! [byte!]] unit [integer!] type [integer!] return: [cell!]] 
                get-value-int: "red/vector/get-value-int" [p [pointer! [integer!]] unit [integer!] return: [integer!]] 
                get-value-float: "red/vector/get-value-float" [p [pointer! [byte!]] unit [integer!] return: [float!]] 
                set-value: "red/vector/set-value" [p [pointer! [byte!]] value [cell!] unit [integer!]]
            ]]
    ] 
    word: context [
        #import [libRedRT-file stdcall [
                push: "red/word/push" [word [red-word!] return: [red-word!]] 
                get: "red/word/get" [word [red-word!] return: [cell!]] 
                get-local: "red/word/get-local" [node [pointer! [integer!]] index [integer!] return: [cell!]] 
                get-any: "red/word/get-any" [word [red-word!] return: [cell!]] 
                get-in: "red/word/get-in" [node [pointer! [integer!]] index [integer!] return: [cell!]] 
                set-in: "red/word/set-in" [node [pointer! [integer!]] index [integer!] return: [cell!]] 
                set-in-ctx: "red/word/set-in-ctx" [node [pointer! [integer!]] index [integer!]] 
                set: "red/word/set" [] 
                replace: "red/word/replace" [node [pointer! [integer!]] index [integer!]] 
                from: "red/word/from" [node [pointer! [integer!]] index [integer!] return: [red-word!]] 
                load: "red/word/load" [str [c-string!] return: [red-word!]] 
                push-local: "red/word/push-local" [node [pointer! [integer!]] index [integer!] return: [red-word!]] 
                duplicate: "red/word/duplicate" [w [red-word!] return: [red-word!]] 
                push*: "red/word/push*" [id [integer!] return: [red-word!]] 
                make-at: "red/word/make-at" [id [integer!] pos [cell!] return: [red-word!]] 
                push-in: "red/word/push-in" [id [integer!] blk [red-block!] return: [red-word!]]
            ]]
    ] 
    _context: context [
        #import [libRedRT-file stdcall [
                get: "red/_context/get" [word [red-word!] return: [cell!]] 
                clone-words: "red/_context/clone-words" [slot [red-block!] type [context-type!] return: [pointer! [integer!]]] 
                set-integer: "red/_context/set-integer" [word [red-word!] value [integer!] return: [integer!]] 
                set: "red/_context/set" [word [red-word!] value [cell!] return: [cell!]]
            ]]
    ] 
    handle: context [
        #import [libRedRT-file stdcall [
                box: "red/handle/box" [value [integer!] type [integer!] return: [red-handle!]] 
                make-in: "red/handle/make-in" [parent [red-block!] value [integer!] type [integer!] return: [red-handle!]]
            ]]
    ] 
    _series: context [
        #import [libRedRT-file stdcall [
                copy: "red/_series/copy" [ser [red-series!] new [red-series!] part-arg [cell!] deep? [logic!] types [cell!] return: [red-series!]] 
                remove: "red/_series/remove" [ser [red-series!] part-arg [cell!] key-arg [cell!] return: [red-series!]]
            ]]
    ] 
    symbol: context [
        #import [libRedRT-file stdcall [
                make: "red/symbol/make" [s [c-string!] return: [integer!]] 
                resolve: "red/symbol/resolve" [id [integer!] return: [integer!]]
            ]]
    ] 
    unicode: context [
        #import [libRedRT-file stdcall [
                load-utf8: "red/unicode/load-utf8" [src [c-string!] size [integer!] return: [pointer! [integer!]]] 
                decode-utf8-char: "red/unicode/decode-utf8-char" [src [c-string!] cnt [pointer! [integer!]] return: [integer!]] 
                to-utf8: "red/unicode/to-utf8" [str [red-string!] len [pointer! [integer!]] return: [c-string!]] 
                load-utf8-buffer: "red/unicode/load-utf8-buffer" [src [c-string!] size [integer!] dst [series-buffer!] remain [pointer! [integer!]] convert? [logic!] return: [pointer! [integer!]]] 
                load-utf8-stream: "red/unicode/load-utf8-stream" [src [c-string!] size [integer!] output [red-string!] remain [pointer! [integer!]] return: [pointer! [integer!]]]
            ]]
    ] 
    natives: context [
        #import [libRedRT-file stdcall [
                remove-each-init: "red/natives/remove-each-init" [] 
                remove-each-next: "red/natives/remove-each-next" [size [integer!]] 
                foreach-next-block: "red/natives/foreach-next-block" [size [integer!] return: [logic!]] 
                foreach-next: "red/natives/foreach-next" [return: [logic!]] 
                forall-next?: "red/natives/forall-next?" [return: [logic!]] 
                forall-end: "red/natives/forall-end" [] 
                forall-end-adjust: "red/natives/forall-end-adjust" [] 
                coerce-counter*: "red/natives/coerce-counter*" [] 
                inc-counter: "red/natives/inc-counter" [w [red-word!]] 
                if*: "red/natives/if*" [check? [logic!]] 
                unless*: "red/natives/unless*" [check? [logic!]] 
                either*: "red/natives/either*" [check? [logic!]] 
                any*: "red/natives/any*" [check? [logic!]] 
                all*: "red/natives/all*" [check? [logic!]] 
                while*: "red/natives/while*" [check? [logic!]] 
                until*: "red/natives/until*" [check? [logic!]] 
                loop*: "red/natives/loop*" [check? [logic!]] 
                repeat*: "red/natives/repeat*" [check? [logic!]] 
                forever*: "red/natives/forever*" [check? [logic!]] 
                foreach*: "red/natives/foreach*" [check? [logic!]] 
                forall*: "red/natives/forall*" [check? [logic!]] 
                func*: "red/natives/func*" [check? [logic!]] 
                function*: "red/natives/function*" [check? [logic!]] 
                does*: "red/natives/does*" [check? [logic!]] 
                has*: "red/natives/has*" [check? [logic!]] 
                switch*: "red/natives/switch*" [check? [logic!] default? [integer!]] 
                case*: "red/natives/case*" [check? [logic!] all? [integer!]] 
                do*: "red/natives/do*" [check? [logic!] expand? [integer!] args [integer!] next [integer!] trace [integer!] return: [integer!]] 
                get*: "red/natives/get*" [check? [logic!] any? [integer!] case? [integer!]] 
                set*: "red/natives/set*" [check? [logic!] _any? [integer!] case? [integer!] _only? [integer!] _some? [integer!]] 
                print*: "red/natives/print*" [check? [logic!]] 
                prin*: "red/natives/prin*" [check? [logic!]] 
                equal?*: "red/natives/equal?*" [check? [logic!] return: [red-logic!]] 
                not-equal?*: "red/natives/not-equal?*" [check? [logic!] return: [red-logic!]] 
                strict-equal?*: "red/natives/strict-equal?*" [check? [logic!] return: [red-logic!]] 
                lesser?*: "red/natives/lesser?*" [check? [logic!] return: [red-logic!]] 
                greater?*: "red/natives/greater?*" [check? [logic!] return: [red-logic!]] 
                lesser-or-equal?*: "red/natives/lesser-or-equal?*" [check? [logic!] return: [red-logic!]] 
                greater-or-equal?*: "red/natives/greater-or-equal?*" [check? [logic!] return: [red-logic!]] 
                same?*: "red/natives/same?*" [check? [logic!] return: [red-logic!]] 
                not*: "red/natives/not*" [check? [logic!]] 
                type?*: "red/natives/type?*" [check? [logic!] word? [integer!] return: [cell!]] 
                reduce*: "red/natives/reduce*" [check? [logic!] into [integer!]] 
                compose*: "red/natives/compose*" [check? [logic!] deep [integer!] only [integer!] into [integer!]] 
                stats*: "red/natives/stats*" [check? [logic!] show [integer!] info [integer!]] 
                bind*: "red/natives/bind*" [check? [logic!] copy [integer!]] 
                in*: "red/natives/in*" [check? [logic!]] 
                parse*: "red/natives/parse*" [check? [logic!] case? [integer!] part [integer!] trace [integer!] return: [integer!]] 
                union*: "red/natives/union*" [check? [logic!] cased [integer!] skip [integer!]] 
                intersect*: "red/natives/intersect*" [check? [logic!] cased [integer!] skip [integer!]] 
                unique*: "red/natives/unique*" [check? [logic!] cased [integer!] skip [integer!]] 
                difference*: "red/natives/difference*" [check? [logic!] cased [integer!] skip [integer!]] 
                exclude*: "red/natives/exclude*" [check? [logic!] cased [integer!] skip [integer!]] 
                complement?*: "red/natives/complement?*" [check? [logic!] return: [red-logic!]] 
                dehex*: "red/natives/dehex*" [check? [logic!] return: [red-string!]] 
                enhex*: "red/natives/enhex*" [check? [logic!] return: [red-string!]] 
                negative?*: "red/natives/negative?*" [check? [logic!] return: [red-logic!]] 
                positive?*: "red/natives/positive?*" [check? [logic!] return: [red-logic!]] 
                max*: "red/natives/max*" [check? [logic!]] 
                min*: "red/natives/min*" [check? [logic!]] 
                shift*: "red/natives/shift*" [check? [logic!] left [integer!] logical [integer!]] 
                to-hex*: "red/natives/to-hex*" [check? [logic!] size [integer!]] 
                sine*: "red/natives/sine*" [check? [logic!] radians [integer!]] 
                cosine*: "red/natives/cosine*" [check? [logic!] radians [integer!]] 
                tangent*: "red/natives/tangent*" [check? [logic!] radians [integer!]] 
                arcsine*: "red/natives/arcsine*" [check? [logic!] radians [integer!]] 
                arccosine*: "red/natives/arccosine*" [check? [logic!] radians [integer!]] 
                arctangent*: "red/natives/arctangent*" [check? [logic!] radians [integer!]] 
                arctangent2*: "red/natives/arctangent2*" [check? [logic!] radians [integer!]] 
                NaN?*: "red/natives/NaN?*" [check? [logic!] return: [red-logic!]] 
                log-2*: "red/natives/log-2*" [check? [logic!]] 
                log-10*: "red/natives/log-10*" [check? [logic!]] 
                log-e*: "red/natives/log-e*" [check? [logic!]] 
                exp*: "red/natives/exp*" [check? [logic!]] 
                square-root*: "red/natives/square-root*" [check? [logic!]] 
                construct*: "red/natives/construct*" [check? [logic!] _with [integer!] only [integer!]] 
                value?*: "red/natives/value?*" [check? [logic!]] 
                try*: "red/natives/try*" [check? [logic!] _all [integer!] keep [integer!] return: [integer!]] 
                uppercase*: "red/natives/uppercase*" [check? [logic!] part [integer!]] 
                lowercase*: "red/natives/lowercase*" [check? [logic!] part [integer!]] 
                as-pair*: "red/natives/as-pair*" [check? [logic!]] 
                break*: "red/natives/break*" [check? [logic!] returned [integer!]] 
                continue*: "red/natives/continue*" [check? [logic!]] 
                exit*: "red/natives/exit*" [check? [logic!]] 
                return*: "red/natives/return*" [check? [logic!]] 
                throw*: "red/natives/throw*" [check? [logic!] name [integer!]] 
                catch*: "red/natives/catch*" [check? [logic!] name [integer!]] 
                extend*: "red/natives/extend*" [check? [logic!] case? [integer!]] 
                debase*: "red/natives/debase*" [check? [logic!] base-arg [integer!]] 
                to-local-file*: "red/natives/to-local-file*" [check? [logic!] full? [integer!]] 
                wait*: "red/natives/wait*" [check? [logic!] all? [integer!]] 
                checksum*: "red/natives/checksum*" [check? [logic!] _with [integer!]] 
                unset*: "red/natives/unset*" [check? [logic!]] 
                new-line*: "red/natives/new-line*" [check? [logic!] _all [integer!] skip [integer!]] 
                new-line?*: "red/natives/new-line?*" [check? [logic!]] 
                enbase*: "red/natives/enbase*" [check? [logic!] base-arg [integer!]] 
                handle-thrown-error: "red/natives/handle-thrown-error" [keep? [logic!]] 
                now*: "red/natives/now*" [check? [logic!] year [integer!] month [integer!] day [integer!] time [integer!] zone [integer!] _date [integer!] weekday [integer!] yearday [integer!] precise [integer!] utc [integer!]] 
                get-env*: "red/natives/get-env*" [check? [logic!]] 
                set-env*: "red/natives/set-env*" [check? [logic!]] 
                list-env*: "red/natives/list-env*" [check? [logic!]] 
                sign?*: "red/natives/sign?*" [check? [logic!] return: [red-integer!]] 
                as*: "red/natives/as*" [check? [logic!]] 
                call*: "red/natives/call*" [check? [logic!] wait [integer!] show [integer!] console [integer!] shell [integer!] input [integer!] output [integer!] error [integer!] return: [red-integer!]] 
                zero?*: "red/natives/zero?*" [check? [logic!] return: [red-logic!]] 
                size?*: "red/natives/size?*" [check? [logic!]] 
                browse*: "red/natives/browse*" [check? [logic!]] 
                context?*: "red/natives/context?*" [check? [logic!]] 
                compress*: "red/natives/compress*" [check? [logic!]] 
                decompress*: "red/natives/decompress*" [check? [logic!] size [integer!]] 
                recycle*: "red/natives/recycle*" [check? [logic!] on? [integer!] off? [integer!]] 
                transcode*: "red/natives/transcode*" [check? [logic!] next [integer!] one [integer!] prescan [integer!] scan [integer!] part [integer!] into [integer!] trace [integer!]] 
                as-money*: "red/natives/as-money*" [check? [logic!]] 
                apply*: "red/natives/apply*" [check? [logic!] _all [integer!] safer [integer!]] 
                as-point2D*: "red/natives/as-point2D*" [check? [logic!]] 
                as-point3D*: "red/natives/as-point3D*" [check? [logic!]] 
                buffer-blk: "red/natives/buffer-blk" [red-block!]
            ]]
    ] 
    actions: context [
        #import [libRedRT-file stdcall [
                make*: "red/actions/make*" [return: [cell!]] 
                random*: "red/actions/random*" [seed [integer!] secure [integer!] only [integer!] return: [cell!]] 
                reflect*: "red/actions/reflect*" [return: [red-block!]] 
                to*: "red/actions/to*" [return: [cell!]] 
                form*: "red/actions/form*" [part [integer!]] 
                mold*: "red/actions/mold*" [only [integer!] _all [integer!] flat [integer!] part [integer!]] 
                eval-path*: "red/actions/eval-path*" [set? [logic!] return: [cell!]] 
                compare: "red/actions/compare" [value1 [cell!] value2 [cell!] op [comparison-op!] return: [logic!]] 
                absolute*: "red/actions/absolute*" [return: [cell!]] 
                add*: "red/actions/add*" [return: [cell!]] 
                divide*: "red/actions/divide*" [return: [cell!]] 
                multiply*: "red/actions/multiply*" [return: [cell!]] 
                negate*: "red/actions/negate*" [return: [cell!]] 
                power*: "red/actions/power*" [return: [cell!]] 
                remainder*: "red/actions/remainder*" [return: [cell!]] 
                round*: "red/actions/round*" [_to [integer!] even [integer!] down [integer!] half-down [integer!] floor [integer!] ceil [integer!] half-ceil [integer!]] 
                subtract*: "red/actions/subtract*" [return: [cell!]] 
                even?*: "red/actions/even?*" [return: [red-logic!]] 
                odd?*: "red/actions/odd?*" [return: [red-logic!]] 
                and~*: "red/actions/and~*" [return: [cell!]] 
                complement*: "red/actions/complement*" [] 
                or~*: "red/actions/or~*" [return: [cell!]] 
                xor~*: "red/actions/xor~*" [return: [cell!]] 
                append*: "red/actions/append*" [part [integer!] only [integer!] dup [integer!]] 
                at*: "red/actions/at*" [return: [cell!]] 
                back*: "red/actions/back*" [return: [cell!]] 
                change*: "red/actions/change*" [part [integer!] only [integer!] dup [integer!] return: [red-series!]] 
                clear*: "red/actions/clear*" [return: [cell!]] 
                copy*: "red/actions/copy*" [part [integer!] deep [integer!] types [integer!] return: [cell!]] 
                find*: "red/actions/find*" [part [integer!] only [integer!] case-arg [integer!] same-arg [integer!] any-arg [integer!] with-arg [integer!] skip [integer!] last [integer!] reverse [integer!] tail [integer!] match [integer!]] 
                head*: "red/actions/head*" [return: [cell!]] 
                head?*: "red/actions/head?*" [return: [cell!]] 
                index?*: "red/actions/index?*" [return: [cell!]] 
                insert*: "red/actions/insert*" [part [integer!] only [integer!] dup [integer!]] 
                move*: "red/actions/move*" [part [integer!] return: [cell!]] 
                length?*: "red/actions/length?*" [return: [red-integer!]] 
                next*: "red/actions/next*" [return: [cell!]] 
                pick*: "red/actions/pick*" [return: [cell!]] 
                poke*: "red/actions/poke*" [return: [cell!]] 
                put*: "red/actions/put*" [case? [integer!]] 
                remove*: "red/actions/remove*" [part [integer!] key [integer!]] 
                reverse*: "red/actions/reverse*" [part [integer!] skip [integer!]] 
                select*: "red/actions/select*" [part [integer!] only [integer!] case-arg [integer!] same-arg [integer!] any-arg [integer!] with-arg [integer!] skip [integer!] last [integer!] reverse [integer!]] 
                sort*: "red/actions/sort*" [case-arg [integer!] skip-arg [integer!] compare [integer!] part [integer!] all-arg [integer!] reverse [integer!] stable [integer!]] 
                skip*: "red/actions/skip*" [return: [cell!]] 
                swap*: "red/actions/swap*" [return: [red-series!]] 
                tail*: "red/actions/tail*" [return: [cell!]] 
                tail?*: "red/actions/tail?*" [return: [cell!]] 
                take*: "red/actions/take*" [part [integer!] deep [integer!] last [integer!] return: [cell!]] 
                trim*: "red/actions/trim*" [head [integer!] tail [integer!] auto [integer!] lines [integer!] _all [integer!] with-arg [integer!] return: [red-series!]] 
                create*: "red/actions/create*" [return: [cell!]] 
                close*: "red/actions/close*" [return: [cell!]] 
                delete*: "red/actions/delete*" [return: [cell!]] 
                modify*: "red/actions/modify*" [case? [integer!] return: [cell!]] 
                open*: "red/actions/open*" [new [integer!] read [integer!] write [integer!] seek [integer!] allow [integer!]] 
                query*: "red/actions/query*" [] 
                read*: "red/actions/read*" [part [integer!] seek [integer!] binary? [integer!] lines? [integer!] info? [integer!] as-arg [integer!] return: [cell!]] 
                rename*: "red/actions/rename*" [return: [cell!]] 
                update*: "red/actions/update*" [return: [cell!]] 
                write*: "red/actions/write*" [binary? [integer!] lines? [integer!] info? [integer!] append? [integer!] part [integer!] seek [integer!] allow [integer!] as-arg [integer!] return: [cell!]]
            ]]
    ] 
    ownership: context [
        #import [libRedRT-file stdcall [
                bind: "red/ownership/bind" [container [cell!] owner [red-object!] word [red-word!]] 
                check: "red/ownership/check" [value [cell!] action [red-word!] new [cell!] index [integer!] part [integer!] return: [logic!]]
            ]]
    ] 
    image: context [
        #import [libRedRT-file stdcall [
                init-image: "red/image/init-image" [img [red-image!] handle [pointer! [integer!]] return: [red-image!]] 
                push: "red/image/push" [img [red-image!]] 
                acquire-buffer: "red/image/acquire-buffer" [img [red-image!] bitmap [pointer! [integer!]] return: [pointer! [integer!]]] 
                release-buffer: "red/image/release-buffer" [img [red-image!] bitmap [integer!] modified? [logic!]]
            ]]
    ] 
    OS-image: context [
        #import [libRedRT-file stdcall [
                lock-bitmap: "red/OS-image/lock-bitmap" [img [red-image!] write? [logic!] return: [integer!]] 
                get-data: "red/OS-image/get-data" [handle [integer!] stride [pointer! [integer!]] return: [pointer! [integer!]]] 
                unlock-bitmap: "red/OS-image/unlock-bitmap" [img [red-image!] data [integer!]]
            ]]
    ] collector: context [
        #import [libRedRT-file stdcall [
                active?: "red/collector/active?" [logic!]
            ]]
    ] 
    words: context [
        red/boot?: yes 
        _body: red/word/load "<body>" 
        _anon: red/word/load "<anon>" 
        _remove: red/word/load "remove" 
        _take: red/word/load "take" 
        _clear: red/word/load "clear" 
        _insert: red/word/load "insert" 
        _poke: red/word/load "poke" 
        _put: red/word/load "put" 
        _moved: red/word/load "moved" 
        _changed: red/word/load "changed" 
        _reverse: red/word/load "reverse" 
        _lowercase: red/word/load "lowercase" 
        _uppercase: red/word/load "uppercase" 
        type: red/symbol/make "type" 
        face: red/symbol/make "face" 
        window: red/symbol/make "window" 
        offset: red/symbol/make "offset" 
        key: red/symbol/make "key" 
        picked: red/symbol/make "picked" 
        flags: red/symbol/make "flags" 
        away?: red/symbol/make "away?" 
        down?: red/symbol/make "down?" 
        mid-down?: red/symbol/make "mid-down?" 
        alt-down?: red/symbol/make "alt-down?" 
        aux-down?: red/symbol/make "aux-down?" 
        ctrl?: red/symbol/make "ctrl?" 
        shift?: red/symbol/make "shift?" 
        red/boot?: no
    ]
]