Red[
    Title: "Hello View (resizable)"
    Needs: 'View
]

win: layout [
    title "Hello View"
    below space 8x8

    text "Введите имя:"
    name: field 250 return

    btn: button "Поздороваться" [
        either empty? trim name/text [
            alert "Сначала введите имя 🙂"
        ][
            alert rejoin ["Привет, " trim name/text "!"]
            print rejoin ["[LOG] greeted: " trim name/text]
        ]
    ]

    return
    text "Мини-канвас (draw):"
    canvas: base 400x120 white draw [
        pen black box 10x10 390x110
        pen blue  line 20x60 380x60
        pen red   circle 80x60 20
        pen green text 120x50 "Red/View!"
    ]

    return
    button "Переименовать окно" [
        face/parent/title: rejoin ["Hello View — " now/time]
        show face/parent
    ]
    button "Выход" [quit]
]

; делаем окно ресайзабельным и подгоняем канвас при изменении размера
view/options win [
    size: 520x320
    resize: true
    actors: object [
        on-resize: func [face event][
            ; поля слева фиксированной ширины, растягиваем только канвас по ширине и немного по высоте
            canvas/size: as-pair max 200 face/size/x - 40  max 80 face/size/y - 180
            show canvas
        ]
    ]
]
